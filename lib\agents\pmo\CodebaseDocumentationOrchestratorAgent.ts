/**
 * Codebase Documentation Orchestrator Agent
 * 
 * This agent coordinates comprehensive documentation generation for codebases
 * by analyzing structure and deploying specialized sub-agents for different
 * aspects of documentation.
 */

import { processWithAnthropic } from '../../tools/anthropic-ai';
import { processWithGoogleAI } from '../../tools/google-ai';
import {
  CodebaseDocumentationTeamAgentResult
} from './TeamAgentInterfaces';
import { v4 as uuidv4 } from 'uuid';
import { ChartTool, ChartGenerationResult } from '../../tools/chart-tool';
import { z } from 'zod';

// Custom options interface for Codebase Documentation Orchestrator
export interface CodebaseDocumentationOrchestratorOptions {
  userId: string;
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CodebaseDocumentationStreamUpdate) => void;
  codebasePaths?: string[];
  documentationScope?: 'full' | 'partial' | 'specific';
  outputFormat?: 'markdown' | 'html' | 'pdf';
  includeArchitecture?: boolean;
  includeApiDocs?: boolean;
  includeDataFlow?: boolean;
  maxSubAgents?: number;
}

// Dynamic sub-agent definition for flexible documentation tasks
export interface DynamicSubAgent {
  id: string;
  name: string;
  description: string;
  specialization: string;
  capabilities: string[];
}

export interface SubAgentAssignment {
  agentId: string;
  agentName: string;
  assignment: string;
  priority: 'high' | 'medium' | 'low';
  estimatedComplexity: 'simple' | 'moderate' | 'complex';
  requiredPaths: string[];
  specialization: string;
}

export interface SubAgentResult {
  agentId: string;
  agentName: string;
  assignment: string;
  output: string;
  success: boolean;
  error?: string;
  artifacts?: {
    diagrams?: string[];
    codeSnippets?: string[];
    configurations?: string[];
  };
}

// Documentation assessment interface
export interface DocumentationAssessment {
  isComplete: boolean;
  completionScore: number; // 0-100
  missingAreas: string[];
  qualityIssues: string[];
  recommendedAdditionalAgents: DynamicSubAgent[];
  overallFeedback: string;
}

// Zod schemas for runtime validation
export const DynamicSubAgentSchema = z.object({
  id: z.string().min(1, "Agent ID is required"),
  name: z.string().min(1, "Agent name is required"),
  description: z.string().min(1, "Agent description is required"),
  specialization: z.string().min(1, "Specialization is required"),
  capabilities: z.array(z.string()).min(1, "At least one capability is required")
});

export const SubAgentAssignmentSchema = z.object({
  agentId: z.string().min(1, "Agent ID is required"),
  agentName: z.string().min(1, "Agent name is required"),
  assignment: z.string().min(1, "Assignment description is required"),
  priority: z.enum(['high', 'medium', 'low']),
  estimatedComplexity: z.enum(['simple', 'moderate', 'complex']),
  requiredPaths: z.array(z.string()),
  specialization: z.string().min(1, "Specialization is required")
});

export const DocumentationAssessmentSchema = z.object({
  isComplete: z.boolean(),
  completionScore: z.number().min(0).max(100),
  missingAreas: z.array(z.string()),
  qualityIssues: z.array(z.string()),
  recommendedAdditionalAgents: z.array(DynamicSubAgentSchema),
  overallFeedback: z.string()
});

export const ChartRecommendationSchema = z.object({
  description: z.string().min(1, "Chart description is required"),
  type: z.enum(['bar', 'line', 'pie', 'area', 'scatter', 'radar', 'composed', 'table', 'flow', 'heatmap', 'bubble']),
  rationale: z.string().min(1, "Chart rationale is required")
});

// Type inference from schemas
export type DynamicSubAgentType = z.infer<typeof DynamicSubAgentSchema>;
export type SubAgentAssignmentType = z.infer<typeof SubAgentAssignmentSchema>;
export type DocumentationAssessmentType = z.infer<typeof DocumentationAssessmentSchema>;
export type ChartRecommendationType = z.infer<typeof ChartRecommendationSchema>;

export interface CodebaseAnalysisResult {
  totalFiles: number;
  totalLines: number;
  languages: string[];
  complexity: 'low' | 'medium' | 'high';
  mainDirectories: string[];
  keyFiles: string[];
  frameworks: string[];
  dependencies: string[];
}

export interface CodebaseDocumentationStreamUpdate {
  stage: 'analyzing-codebase' | 'creating-dynamic-agents' | 'processing-assignments' | 'assessing-documentation' | 'generating-additional-agents' | 'consolidating-results' | 'generating-final-docs' | 'complete';
  data?: any;
  message?: string;
  subAgentProgress?: {
    completed: number;
    total: number;
    currentAgent?: string;
  };
  assessmentIteration?: number;
}

export class CodebaseDocumentationOrchestratorAgent {
  private options: CodebaseDocumentationOrchestratorOptions;
  private subAgentResults: Map<string, SubAgentResult> = new Map();
  private chartTool: ChartTool = new ChartTool();
  private maxAssessmentIterations: number = 3;

  constructor(options: CodebaseDocumentationOrchestratorOptions) {
    this.options = {
      userId: options.userId,
      includeExplanation: options.includeExplanation || false,
      streamResponse: options.streamResponse || false,
      onStreamUpdate: options.onStreamUpdate,
      codebasePaths: options.codebasePaths || [],
      documentationScope: options.documentationScope || 'full',
      outputFormat: options.outputFormat || 'markdown',
      includeArchitecture: options.includeArchitecture !== false,
      includeApiDocs: options.includeApiDocs !== false,
      includeDataFlow: options.includeDataFlow !== false,
      maxSubAgents: options.maxSubAgents || 9
    };

    if (!this.options.userId) {
      throw new Error("CodebaseDocumentationOrchestratorAgent requires a userId in options.");
    }
  }

  /**
   * Process codebase documentation request with dynamic sub-agent creation and assessment
   */
  public async processDocumentationRequest(
    selectedPaths: string[],
    description: string,
    customContext?: string
  ): Promise<CodebaseDocumentationTeamAgentResult> {
    try {
      this._updateStream('analyzing-codebase', {}, 'Analyzing codebase structure and complexity...');

      // Step 1: Analyze the codebase
      const codebaseAnalysis = await this._analyzeCodebase(selectedPaths);

      this._updateStream('creating-dynamic-agents', { codebaseAnalysis }, 'Creating dynamic sub-agents based on requirements...');

      // Step 2: Dynamically create initial sub-agents based on request analysis
      let currentSubAgents = await this._createDynamicSubAgents(
        selectedPaths,
        description,
        codebaseAnalysis,
        customContext
      );

      let assessmentIteration = 0;
      let isDocumentationComplete = false;
      let allSubAgentResults = new Map<string, SubAgentResult>();

      // Step 3: Iterative documentation generation and assessment
      while (!isDocumentationComplete && assessmentIteration < this.maxAssessmentIterations) {
        assessmentIteration++;

        this._updateStream('processing-assignments', {
          subAgentProgress: { completed: 0, total: currentSubAgents.length },
          assessmentIteration
        }, `Processing sub-agent assignments (iteration ${assessmentIteration})...`);

        // Execute current batch of sub-agents
        const batchResults = await this._executeSubAgentAssignments(currentSubAgents);

        // Merge results with previous iterations
        batchResults.forEach((result, agentId) => {
          allSubAgentResults.set(agentId, result);
        });

        this._updateStream('assessing-documentation', {
          completedAgents: allSubAgentResults.size,
          assessmentIteration
        }, `Assessing documentation completeness (iteration ${assessmentIteration})...`);

        // Step 4: Assess documentation completeness
        const assessment = await this._assessDocumentationCompleteness(
          allSubAgentResults,
          description,
          codebaseAnalysis,
          selectedPaths
        );

        isDocumentationComplete = assessment.isComplete;

        if (!isDocumentationComplete && assessmentIteration < this.maxAssessmentIterations) {
          this._updateStream('generating-additional-agents', {
            missingAreas: assessment.missingAreas,
            recommendedAgents: assessment.recommendedAdditionalAgents.length,
            assessmentIteration
          }, `Creating additional sub-agents for missing areas...`);

          // Create additional sub-agents for missing areas
          currentSubAgents = assessment.recommendedAdditionalAgents.map(agent => ({
            agentId: agent.id,
            agentName: agent.name,
            assignment: `Address missing documentation area: ${agent.description}`,
            priority: 'high' as const,
            estimatedComplexity: 'moderate' as const,
            requiredPaths: selectedPaths,
            specialization: agent.specialization
          }));
        }
      }

      this._updateStream('consolidating-results', {
        totalAgents: allSubAgentResults.size,
        iterations: assessmentIteration
      }, 'Consolidating documentation results...');

      // Step 5: Generate final consolidated documentation
      const documentationArtifacts = await this._consolidateDocumentation(
        allSubAgentResults,
        codebaseAnalysis,
        description
      );

      this._updateStream('generating-final-docs', { documentationArtifacts }, 'Generating final documentation and charts...');

      // Step 6: Generate supporting charts dynamically based on content
      const supportingCharts = await this._generateDynamicDocumentationCharts(
        codebaseAnalysis,
        documentationArtifacts,
        allSubAgentResults
      );

      this._updateStream('complete', {}, 'Documentation generation complete!');

      return {
        success: true,
        taskId: uuidv4(),
        output: this._formatFinalOutput(documentationArtifacts, codebaseAnalysis),
        outputDocumentIds: [], // Will be populated when saved to Firebase
        documentationArtifacts,
        supportingCharts,
        subAgentResults: Array.from(allSubAgentResults.values()).map(result => ({
          agentType: result.agentName, // Use agentName for backward compatibility
          assignment: result.assignment,
          output: result.output,
          success: result.success,
          error: result.error ? { error: result.error } : { error: null }
        })),
        codebaseMetrics: codebaseAnalysis
      };

    } catch (error: any) {
      console.error('CodebaseDocumentationOrchestratorAgent: Error processing request:', error);
      return {
        success: false,
        taskId: uuidv4(),
        output: '',
        outputDocumentIds: [],
        error: error.message || 'Failed to process codebase documentation request'
      };
    }
  }

  /**
   * Analyze codebase structure and complexity
   */
  private async _analyzeCodebase(selectedPaths: string[]): Promise<CodebaseAnalysisResult> {
    // In a real implementation, this would:
    // 1. Scan the file system for the selected paths
    // 2. Analyze file types, sizes, and structure
    // 3. Detect frameworks and dependencies
    // 4. Calculate complexity metrics

    // Mock implementation for now
    return {
      totalFiles: 150,
      totalLines: 25000,
      languages: ['TypeScript', 'JavaScript', 'CSS', 'HTML'],
      complexity: 'medium',
      mainDirectories: ['src', 'components', 'lib', 'app'],
      keyFiles: ['package.json', 'tsconfig.json', 'next.config.js'],
      frameworks: ['Next.js', 'React', 'Tailwind CSS'],
      dependencies: ['react', 'next', 'typescript', 'tailwindcss']
    };
  }

  /**
   * Dynamically create sub-agents based on documentation requirements
   */
  private async _createDynamicSubAgents(
    selectedPaths: string[],
    description: string,
    codebaseAnalysis: CodebaseAnalysisResult,
    customContext?: string
  ): Promise<SubAgentAssignment[]> {
    const prompt = `
You are a Documentation Strategy AI. Based on the user's documentation request and codebase analysis,
dynamically determine which specialized sub-agents are needed to fulfill the documentation requirements.

CODEBASE ANALYSIS:
- Total Files: ${codebaseAnalysis.totalFiles}
- Languages: ${codebaseAnalysis.languages.join(', ')}
- Complexity: ${codebaseAnalysis.complexity}
- Frameworks: ${codebaseAnalysis.frameworks.join(', ')}
- Main Directories: ${codebaseAnalysis.mainDirectories.join(', ')}

USER DOCUMENTATION REQUEST:
${description}

SELECTED PATHS:
${selectedPaths.join('\n')}

${customContext ? `ADDITIONAL CONTEXT:\n${customContext}` : ''}

Based on this information, create a list of specialized sub-agents that are specifically needed for this documentation request.
Do NOT create a fixed set of agents - only create agents that are relevant to the specific request and codebase.

For each agent, provide:
1. A unique ID (kebab-case)
2. A descriptive name
3. A specific assignment tailored to this request
4. Priority level (high/medium/low)
5. Complexity estimate (simple/moderate/complex)
6. Required paths from the selected paths
7. Specialization area

Return a JSON array with this structure:
{
  "agentId": "unique-agent-id",
  "agentName": "Descriptive Agent Name",
  "assignment": "Specific task description for this documentation request",
  "priority": "high|medium|low",
  "estimatedComplexity": "simple|moderate|complex",
  "requiredPaths": ["relevant", "paths", "only"],
  "specialization": "Area of expertise"
}

Focus on creating only the agents that are actually needed for this specific request.
`;

    try {
      const result = await processWithAnthropic({
        prompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.3,
          maxTokens: 3000
        }
      });

      const rawAssignments = JSON.parse(result.trim());

      // Validate and parse assignments using Zod
      const validatedAssignments: SubAgentAssignment[] = [];
      for (const assignment of rawAssignments) {
        try {
          const validated = SubAgentAssignmentSchema.parse(assignment);
          validatedAssignments.push(validated);
        } catch (validationError) {
          console.warn('Invalid assignment from LLM, skipping:', assignment, validationError);
        }
      }

      if (validatedAssignments.length === 0) {
        console.warn('No valid assignments from LLM, using fallback');
        return this._createFallbackAssignments(selectedPaths);
      }

      return validatedAssignments.slice(0, this.options.maxSubAgents || 9);
    } catch (error: any) {
      console.error('Failed to create dynamic sub-agent assignments with Claude, using fallback:', error);
      return this._createFallbackAssignments(selectedPaths);
    }
  }

  /**
   * Assess documentation completeness using Google AI
   */
  private async _assessDocumentationCompleteness(
    subAgentResults: Map<string, SubAgentResult>,
    originalDescription: string,
    codebaseAnalysis: CodebaseAnalysisResult,
    selectedPaths: string[]
  ): Promise<DocumentationAssessment> {
    const completedDocumentation = Array.from(subAgentResults.values())
      .filter(result => result.success)
      .map(result => `**${result.agentName}**: ${result.output.substring(0, 500)}...`)
      .join('\n\n');

    const prompt = `
You are a Documentation Assessment AI using Google's Gemini model. Evaluate the completeness and quality of the generated documentation.

ORIGINAL DOCUMENTATION REQUEST:
${originalDescription}

CODEBASE ANALYSIS:
- Total Files: ${codebaseAnalysis.totalFiles}
- Languages: ${codebaseAnalysis.languages.join(', ')}
- Complexity: ${codebaseAnalysis.complexity}
- Frameworks: ${codebaseAnalysis.frameworks.join(', ')}

COMPLETED DOCUMENTATION:
${completedDocumentation}

SELECTED PATHS:
${selectedPaths.join('\n')}

Assess the documentation and provide:

1. **Completion Score** (0-100): How well does the documentation fulfill the original request?
2. **Missing Areas**: What important aspects are not covered?
3. **Quality Issues**: Any problems with the existing documentation?
4. **Additional Agents Needed**: If incomplete, what specialized agents should be created?

For any recommended additional agents, provide:
- Unique ID (kebab-case)
- Descriptive name
- Specific description of what they should document
- Specialization area
- Capabilities list

Return a JSON object with this structure:
{
  "isComplete": boolean,
  "completionScore": number,
  "missingAreas": ["area1", "area2"],
  "qualityIssues": ["issue1", "issue2"],
  "recommendedAdditionalAgents": [
    {
      "id": "agent-id",
      "name": "Agent Name",
      "description": "What this agent should document",
      "specialization": "Area of expertise",
      "capabilities": ["capability1", "capability2"]
    }
  ],
  "overallFeedback": "Summary of assessment"
}

Be thorough but practical. Only recommend additional agents if they would significantly improve the documentation.
`;

    try {
      const result = await processWithGoogleAI({
        prompt,
        model: 'gemini-2.5-pro'
      });

      // Handle potential fallback response
      if (result.startsWith('FALLBACK_REQUIRED:')) {
        console.warn('Google AI fallback triggered, using Claude for assessment');
        const claudeResult = await processWithAnthropic({
          prompt,
          model: "claude-sonnet-4-20250514",
          modelOptions: {
            temperature: 0.3,
            maxTokens: 2000
          }
        });

        try {
          const rawAssessment = JSON.parse(claudeResult.trim());
          return DocumentationAssessmentSchema.parse(rawAssessment);
        } catch (validationError) {
          console.error('Invalid assessment from Claude fallback:', validationError);
          throw validationError;
        }
      }

      try {
        const rawAssessment = JSON.parse(result.trim());
        return DocumentationAssessmentSchema.parse(rawAssessment);
      } catch (validationError) {
        console.error('Invalid assessment from Google AI:', validationError);
        throw validationError;
      }
    } catch (error: any) {
      console.error('Failed to assess documentation completeness:', error);
      // Return a default "complete" assessment to avoid infinite loops
      return {
        isComplete: true,
        completionScore: 75,
        missingAreas: [],
        qualityIssues: [],
        recommendedAdditionalAgents: [],
        overallFeedback: 'Assessment failed, assuming documentation is adequate.'
      };
    }
  }

  /**
   * Execute all sub-agent assignments
   */
  private async _executeSubAgentAssignments(
    assignments: SubAgentAssignment[]
  ): Promise<Map<string, SubAgentResult>> {
    const results = new Map<string, SubAgentResult>();

    for (let i = 0; i < assignments.length; i++) {
      const assignment = assignments[i];

      this._updateStream('processing-assignments', {
        subAgentProgress: {
          completed: i,
          total: assignments.length,
          currentAgent: assignment.agentName
        }
      }, `Processing ${assignment.agentName}...`);

      try {
        const result = await this._executeSubAgentAssignment(assignment);
        results.set(assignment.agentId, result);
      } catch (error: any) {
        console.error(`Failed to execute ${assignment.agentName}:`, error);
        results.set(assignment.agentId, {
          agentId: assignment.agentId,
          agentName: assignment.agentName,
          assignment: assignment.assignment,
          output: '',
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Execute a single sub-agent assignment
   */
  private async _executeSubAgentAssignment(assignment: SubAgentAssignment): Promise<SubAgentResult> {
    const prompt = this._getSubAgentPrompt(assignment);

    try {
      const result = await processWithAnthropic({
        prompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.4,
          maxTokens: 2500
        }
      });

      return {
        agentId: assignment.agentId,
        agentName: assignment.agentName,
        assignment: assignment.assignment,
        output: result,
        success: true
      };
    } catch (error: any) {
      throw new Error(`Sub-agent ${assignment.agentName} failed: ${error.message}`);
    }
  }

  /**
   * Get specialized prompt for dynamic sub-agents with enhanced Claude reasoning
   */
  private _getSubAgentPrompt(assignment: SubAgentAssignment): string {
    const basePrompt = `You are a ${assignment.agentName} specialized in ${assignment.specialization} with deep technical expertise in codebase documentation.

AGENT SPECIALIZATION: ${assignment.specialization}

SPECIFIC ASSIGNMENT: ${assignment.assignment}

REQUIRED PATHS: ${assignment.requiredPaths.join(', ')}

Please analyze the codebase systematically and provide comprehensive documentation. Use your reasoning capabilities to:
1. Identify patterns and relationships specific to your specialization
2. Infer architectural decisions and their rationale
3. Highlight potential issues or improvements in your area of expertise
4. Provide context for technical choices relevant to your specialization
5. Focus on delivering actionable insights for your assigned documentation area

Provide detailed, well-structured documentation that directly addresses your specific assignment.
Use markdown formatting for clarity and include code examples where relevant.

Your response should be comprehensive yet focused on your specialization area.
`;

    return basePrompt;
  }

  /**
   * Create fallback assignments if LLM generation fails
   */
  private _createFallbackAssignments(selectedPaths: string[]): SubAgentAssignment[] {
    return [
      {
        agentId: 'project-overview-analyst',
        agentName: 'Project Overview Analyst',
        assignment: 'Analyze project structure and provide high-level overview',
        priority: 'high',
        estimatedComplexity: 'simple',
        requiredPaths: selectedPaths.slice(0, 3),
        specialization: 'Project Analysis'
      },
      {
        agentId: 'architecture-analyst',
        agentName: 'Architecture Analyst',
        assignment: 'Document system architecture and design patterns',
        priority: 'high',
        estimatedComplexity: 'complex',
        requiredPaths: selectedPaths,
        specialization: 'System Architecture'
      }
      // Add more fallback assignments as needed
    ];
  }

  /**
   * Consolidate all sub-agent results into final documentation
   */
  private async _consolidateDocumentation(
    subAgentResults: Map<string, SubAgentResult>,
    codebaseAnalysis: CodebaseAnalysisResult,
    originalDescription: string
  ): Promise<any> {
    // Consolidate results into structured documentation artifacts
    const artifacts: any = {};

    subAgentResults.forEach((result, agentId) => {
      if (result.success) {
        // Use agent specialization to categorize the output
        const specialization = result.agentName.toLowerCase();

        if (specialization.includes('overview') || specialization.includes('project')) {
          artifacts.projectOverview = result.output;
        } else if (specialization.includes('architecture')) {
          artifacts.architectureAnalysis = result.output;
        } else if (specialization.includes('component') || specialization.includes('mapping')) {
          artifacts.componentMapping = result.output;
        } else if (specialization.includes('business') || specialization.includes('logic')) {
          artifacts.businessLogicAnalysis = result.output;
        } else if (specialization.includes('data') || specialization.includes('flow')) {
          artifacts.dataFlowDiagram = result.output;
        } else if (specialization.includes('environment') || specialization.includes('technical')) {
          artifacts.technicalEnvironment = result.output;
        } else if (specialization.includes('dependencies') || specialization.includes('dependency')) {
          artifacts.dependenciesAnalysis = result.output;
        } else if (specialization.includes('api') || specialization.includes('interface')) {
          artifacts.apiDocumentation = result.output;
        } else if (specialization.includes('configuration') || specialization.includes('config')) {
          artifacts.configurationAnalysis = result.output;
        } else {
          // For dynamic agents, create a section based on their name
          const sectionKey = agentId.replace(/-/g, '');
          artifacts[sectionKey] = result.output;
        }
      }
    });

    return artifacts;
  }

  /**
   * Generate supporting charts dynamically based on documentation content
   */
  private async _generateDynamicDocumentationCharts(
    codebaseAnalysis: CodebaseAnalysisResult,
    artifacts: any,
    subAgentResults: Map<string, SubAgentResult>
  ): Promise<ChartGenerationResult[]> {
    const charts: ChartGenerationResult[] = [];

    try {
      // Use Claude to determine what charts would be most valuable
      const chartRecommendationPrompt = `
You are a Data Visualization Expert. Based on the codebase analysis and documentation content,
determine what charts and visualizations would be most valuable for this documentation.

CODEBASE ANALYSIS:
- Total Files: ${codebaseAnalysis.totalFiles}
- Languages: ${codebaseAnalysis.languages.join(', ')}
- Complexity: ${codebaseAnalysis.complexity}
- Frameworks: ${codebaseAnalysis.frameworks.join(', ')}

DOCUMENTATION SECTIONS AVAILABLE:
${Object.keys(artifacts).map(key => `- ${key}`).join('\n')}

AGENT SPECIALIZATIONS:
${Array.from(subAgentResults.values()).map(result => `- ${result.agentName}: ${result.assignment.substring(0, 100)}...`).join('\n')}

Recommend 2-4 specific charts that would enhance this documentation. For each chart, provide:
1. A natural language description of what to visualize
2. The type of chart (pie, bar, line, scatter, etc.)
3. Why this chart would be valuable

Return a JSON array with this structure:
[
  {
    "description": "Natural language description for chart generation",
    "type": "chart_type",
    "rationale": "Why this chart is valuable"
  }
]
`;

      const chartRecommendations = await processWithAnthropic({
        prompt: chartRecommendationPrompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.4,
          maxTokens: 1500
        }
      });

      let recommendations: ChartRecommendationType[] = [];
      try {
        const rawRecommendations = JSON.parse(chartRecommendations.trim());

        // Validate each recommendation using Zod
        for (const rec of rawRecommendations) {
          try {
            const validated = ChartRecommendationSchema.parse(rec);
            recommendations.push(validated);
          } catch (validationError) {
            console.warn('Invalid chart recommendation from LLM, skipping:', rec, validationError);
          }
        }

        if (recommendations.length === 0) {
          throw new Error('No valid recommendations');
        }
      } catch (error) {
        console.warn('Failed to parse chart recommendations, using defaults');
        recommendations = [
          {
            description: `Create a pie chart showing the language distribution for a codebase with the following languages: ${codebaseAnalysis.languages.join(', ')}. Use appropriate colors and show percentages.`,
            type: 'pie',
            rationale: 'Shows technology stack composition'
          },
          {
            description: `Create a bar chart showing codebase metrics with the following data: Total Files: ${codebaseAnalysis.totalFiles}, Total Lines: ${codebaseAnalysis.totalLines.toLocaleString()}. Make it visually appealing with appropriate colors.`,
            type: 'bar',
            rationale: 'Provides scale overview'
          }
        ];
      }

      // Generate each recommended chart
      for (const recommendation of recommendations.slice(0, 4)) { // Limit to 4 charts
        try {
          const chart = await this.chartTool.generateChart({
            prompt: recommendation.description,
            chartType: recommendation.type
          });

          if (chart.success) {
            charts.push(chart);
          }
        } catch (error) {
          console.warn(`Failed to generate chart: ${recommendation.description}`, error);
        }
      }

    } catch (error) {
      console.warn('Failed to generate dynamic documentation charts:', error);
    }

    return charts;
  }

  /**
   * Format final output for display
   */
  private _formatFinalOutput(artifacts: any, codebaseAnalysis: CodebaseAnalysisResult): string {
    let output = `# Codebase Documentation\n\n`;
    
    output += `## Overview\n`;
    output += `- **Total Files**: ${codebaseAnalysis.totalFiles}\n`;
    output += `- **Total Lines**: ${codebaseAnalysis.totalLines.toLocaleString()}\n`;
    output += `- **Languages**: ${codebaseAnalysis.languages.join(', ')}\n`;
    output += `- **Complexity**: ${codebaseAnalysis.complexity}\n`;
    output += `- **Frameworks**: ${codebaseAnalysis.frameworks.join(', ')}\n\n`;

    if (artifacts.projectOverview) {
      output += `## Project Overview\n${artifacts.projectOverview}\n\n`;
    }

    if (artifacts.architectureAnalysis) {
      output += `## Architecture Analysis\n${artifacts.architectureAnalysis}\n\n`;
    }

    if (artifacts.componentMapping) {
      output += `## Component Mapping\n${artifacts.componentMapping}\n\n`;
    }

    if (artifacts.businessLogicAnalysis) {
      output += `## Business Logic Analysis\n${artifacts.businessLogicAnalysis}\n\n`;
    }

    if (artifacts.dataFlowDiagram) {
      output += `## Data Flow Analysis\n${artifacts.dataFlowDiagram}\n\n`;
    }

    if (artifacts.technicalEnvironment) {
      output += `## Technical Environment\n${artifacts.technicalEnvironment}\n\n`;
    }

    if (artifacts.dependenciesAnalysis) {
      output += `## Dependencies Analysis\n${artifacts.dependenciesAnalysis}\n\n`;
    }

    if (artifacts.apiDocumentation) {
      output += `## API Documentation\n${artifacts.apiDocumentation}\n\n`;
    }

    if (artifacts.configurationAnalysis) {
      output += `## Configuration Analysis\n${artifacts.configurationAnalysis}\n\n`;
    }

    return output;
  }

  /**
   * Update stream if streaming is enabled
   */
  private _updateStream(
    stage: CodebaseDocumentationStreamUpdate['stage'],
    data?: any,
    message?: string
  ): void {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({
        stage,
        data,
        message
      });
    }
  }
}
